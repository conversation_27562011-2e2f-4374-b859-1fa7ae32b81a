import { Container } from "@/ui-components/layout/container";
import { goToLogout } from "@/utils/logout";
import { SecondaryButton } from "@repo/ui/form-button";
import { InfoLayout } from "@repo/ui/info-layout";

const Page = () => {
  return (
    <Container>
      <div className="col-span-full md:col-start-3 md:col-span-8 lg:col-start-4 lg:col-span-6 py-6 md-py-10">
        <InfoLayout
          className="py-20 md:py-10 xl:py-20"
          icon="/graphics/orange/compliance-x.png"
          iconAlt="verifying deposit"
          title={"Access restricted"}
          description={
            "Due to the platform’s terms and conditions, you are no longer able to access your dashboard nor perform any functions"
          }
        >
          <div className="flex flex-col md:mx-auto">
            <SecondaryButton onClick={goToLogout}>{"Sign out"}</SecondaryButton>
          </div>
        </InfoLayout>
      </div>
    </Container>
  );
};

export default Page;
